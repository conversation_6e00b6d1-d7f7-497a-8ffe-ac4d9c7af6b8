"use client";
import Image from "next/image";
import React, { useEffect, useState, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { sortOrder } from "@/helpers/Sorting";
import {
  apiCall,
  getOapDetail,
  getOapForm,
  getOapFormSections,
  getStudentDetailsById,
  saveOapForm,
} from "@/api/api";
import loader2 from "../../public/loader2.svg";
import loader from "../../public/loader.svg";
import { useAtom } from "jotai";
import {
  applicationId,
  brandLogo,
  consumerAPIKey,
  dateReplacement,
  email,
  nextForm,
  preferredDateFormat,
  preferredLanguage,
  programmeName,
  staticContentsAtom,
  fontSizeAtom,
} from "@/lib/atom";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { AlertCircle } from "lucide-react";
import { FormLayout } from "../custom/formLayout";
import { CrossCircledIcon } from "@radix-ui/react-icons";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { LinkRenderer } from "../custom/linkRender";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
import { useProgress } from "@/lib/progress-context";

export default function ApplicationForm() {
  const [pageDetails, setPageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });
  const searchParams = useSearchParams();
  const agentAccountId = searchParams.get("agentAccountId");
  const agentContactId = searchParams.get("agentContactId");
  const agentContactUserId = searchParams.get("agentContactUserId");
  const accountManagerUserId = searchParams.get("accountManagerUserId");
  const application = searchParams.get("applicationId");
  const [_, setDateReplacement] = useAtom(dateReplacement);

  const router = useRouter();
  const [, setNextFormDetails] = useAtom(nextForm);
  const [, setEmail] = useAtom(email);
  const [, setApplicationId] = useAtom(applicationId);
  const [, setBrandLogo] = useAtom(brandLogo);
  const [, setProgrammeName] = useAtom(programmeName);
  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey, setApiKey] = useAtom(consumerAPIKey);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [pageInfo, setPageInfo] = useState<any>({});
  const [dropdownOptions, setDropdownOptions] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [preferLang] = useAtom(preferredLanguage);
  const [, setStaticContents] = useAtom(staticContentsAtom);

  const [fontSize, setFontSize] = useAtom(fontSizeAtom);
  const { setProgress } = useProgress();

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
  } = useFormContext();

  useEffect(() => {
    const onFocus = () => {
      // Clear all items from local storage when the window is focused
      localStorage.clear();
    };
    onFocus();
  }, []);

  const {
    data: pageQuery,
    isFetching: pageQueryFetching,
    refetch: refetchPageQuery,
  } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      setStaticContents(res?.staticContents);
      setPreferredDateFormat(res?.preferedDateFormat);
      setDateReplacement(res?.replaceWith);
      setApiKey(res?.eipConsumerKey);
      if (res?.fontSize) {
        setFontSize(res.fontSize);
      }
      return res;
    },
    enabled: true,
  });

  const enableAutoDetection = useMemo(
    () => pageQuery?.enableAutoDetection,
    [pageQuery?.enableAutoDetection]
  );

  const {
    data: sectionQuery,
    isFetching: sectionQueryFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [`${pageQuery?.PK}-${pageQuery?.SK}-${pageQuery?.landingForm}`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageQuery?.PK,
          form: pageQuery?.landingForm,
          mode: pageQuery?.SK,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!pageQuery?.PK,
  });
  const {
    data: formQuery,
    isFetching: formQueryFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${sectionQuery?.SK}-${sectionQuery?.section?.[0]?.section}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.section && sectionQuery?.SK)) return;
      let res = await getOapFormSections(
        {
          oap: pageQuery?.PK,
          mode: pageQuery?.SK,
          formName: sectionQuery?.SK,
          sectionName: sectionQuery?.section?.[0]?.section,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!sectionQuery?.SK,
  });

  useEffect(() => {
    refetchPageQuery();
    if (pageDetails.mode && pageDetails.screen && pageQuery) {
      refetchSectionQuery();
      refetchFormQuery();
    }
  }, [preferLang]);

  const { data: applicationData, isFetching: applicationDataFetching } =
    useQuery({
      queryKey: [`getStudentApplicationById`],
      queryFn: async () => {
        let tempObj: any = { oapName: process.env.NEXT_PUBLIC_OAP_NAME };
        if (application) tempObj.applicationId = application;
        if (agentAccountId) tempObj.agentAccountId = agentAccountId;
        if (agentContactId) tempObj.agentContactId = agentContactId;
        if (agentContactUserId) tempObj.agentContactUserId = agentContactUserId;
        if (accountManagerUserId)
          tempObj.accountManagerUserId = accountManagerUserId;
        let res = await getStudentDetailsById({ ...tempObj }, apiKey);
        return res;
      },
      retry: false,
      enabled: !!application,
    });

  const { data: getOapFormSectionsLookup } = useQuery({
    queryKey: [`lookup-data`],
    queryFn: async () => {
      let res = await apiCall("oap/lookup/country", {
        method: "GET",
        key: apiKey,
      });
      return res;
    },
    enabled: !!apiKey,
  });

  useQuery({
    queryKey: [`next-form-screen`],
    queryFn: async () => {
      let tempObj = {};
      formQuery?.fieldData?.forEach((ele: any) => {
        if (ele.action == "nextForm") {
          tempObj = { ...tempObj, ...ele[ele.action] };
        }
      });
      return tempObj;
    },
    enabled: formQuery?.fieldData.length > 0,
  });

  useEffect(() => {
    if (sectionQuery) {
      if (!application) {
        // agent details missing in create application
        if (
          !agentAccountId ||
          agentAccountId == null ||
          agentAccountId == "null" ||
          agentAccountId === '"null"' ||
          !agentContactId ||
          agentContactId == null ||
          agentContactId == "null" ||
          agentContactId === '"null"' ||
          !agentContactUserId ||
          agentContactUserId == null ||
          agentContactUserId === "null" ||
          agentContactUserId === '"null"' ||
          !accountManagerUserId ||
          accountManagerUserId == null ||
          accountManagerUserId == "null" ||
          accountManagerUserId === '"null"'
        ) {
          setShowWarning(true);
          return;
        }
      }
    } else {
      // agent details missing in update application
      if (!agentContactUserId && sectionQuery) {
        setShowWarning(true);
        return;
      }
    }
    setShowWarning(false);
    if (
      applicationData?.applicationId !== application &&
      !pageQuery &&
      !sectionQuery &&
      !formQuery
    )
      return;
    if (
      applicationData?.applicationId === application &&
      pageQuery &&
      formQuery &&
      sectionQuery
    ) {
      (async () => {
        setEmail(applicationData?.email);
        setApplicationId(applicationData?.applicationId);
        setBrandLogo(pageQuery?.logoInfo?.signedUrl);
        setProgrammeName(applicationData?.programDisplayName);

        if (applicationData?.applicationStatus === "submitted") {
          router.push(`/thank-you`);
        } else {
          setLoading(true);
          if (applicationData?.program) {
            setNextFormDetails({
              mode: process.env.NEXT_PUBLIC_OAP_MODE,
              type: applicationData?.applicationType ? "multiple" : "single",
              form: "APPLICATION",
              renewal: "RENEWAL_OAP",
              international: "INTERNATIONAL_OAP",
              oap: process.env.NEXT_PUBLIC_OAP_NAME,
              currentOap: applicationData?.applicationType,
            });
            setValue("ocrReprocessCount", 0);
            if (applicationData?.applicationType) {
              router.push(
                `/form?apply=${applicationData?.applicationType}&step=0`
              );
            } else router.push(`/form?apply=APPLICATION&step=0`);
          } else {
            setNextFormDetails(getNextForm());
            router.push(`/application-filter`);
          }
        }
      })();
    }
  }, [applicationData, application, pageQuery, formQuery, sectionQuery]);

  const getNextForm = () => {
    const formDetail = formQuery?.fieldData?.find(
      (item: any) => item?.type === "button"
    )?.nextForm;

    return formDetail;
  };

  interface DropdownItem {
    label: string;
    value: any;
  }
  const transformDropdowns = (
    formData: Record<string, unknown>
  ): Record<string, any> => {
    const transformedData: Record<string, any> = {};

    for (const [key, value] of Object.entries(formData)) {
      if (value && typeof value === "object") {
        if (!("label" in value) && !("value" in value)) {
          transformedData[key] = value;
          if (Object.keys(value).length == 0) {
            delete transformedData[key];
          }
        } else {
          const labelKey = `${key}DisplayName`;
          transformedData[labelKey] = (value as DropdownItem).label;
          transformedData[key] = (value as DropdownItem).value;
        }
      } else if (value && typeof value !== "object" && value !== "") {
        transformedData[key] = value;
      }
    }
    return transformedData;
  };

  const saveData = async () => {
    const rest = getValues();
    const isValid = await trigger();
    if (!isValid) return;
    setSaving((prev) => !prev);
    let tempObj: any = {
      ...transformDropdowns(rest),
      applicationStatus: "inProgress",
      applicationType: rest?.[getNextForm()?.dependentField]?.value || "",
    };
    if (agentAccountId) tempObj.agentAccountId = agentAccountId;
    if (agentContactId) tempObj.agentContactId = agentContactId;
    if (agentContactUserId) tempObj.agentContactUserId = agentContactUserId;
    if (accountManagerUserId)
      tempObj.accountManagerUserId = accountManagerUserId;

    tempObj.email = tempObj?.email.toLowerCase();
    setValue("email", tempObj?.email.toLowerCase());

    const res = await saveOapForm(
      { ...tempObj, sectionLabel: formQuery?.label },
      { oapName: pageDetails?.screen, mode: pageDetails?.mode },
      apiKey
    );

    if (res?.email && res?.applicationId) {
      setNextFormDetails(getNextForm());
      setEmail(res?.email);
      setApplicationId(res?.applicationId);
      setBrandLogo(pageQuery?.logoInfo?.signedUrl);
      localStorage.setItem("basic-details", JSON.stringify(res));
      if (formQuery?.havePreQualificationQuestions) {
        router.push(`/app-prequalifying-questions`);
        return;
      }
      if (formQuery?.haveCountrySelectPage) {
        router.push(`/location`);
        return;
      } else {
        if (res?.program) {
          setNextFormDetails({
            mode: process.env.NEXT_PUBLIC_OAP_MODE,
            type: res?.applicationType ? "multiple" : "single",
            form: "APPLICATION",
            renewal: "RENEWAL_OAP",
            international: "INTERNATIONAL_OAP",
            oap: process.env.NEXT_PUBLIC_OAP_NAME,
            currentOap: res?.applicationType,
          });
          setValue("ocrReprocessCount", 0);
          if (res?.applicationType) {
            router.push(`/form?apply=${res?.applicationType}&step=0`);
          } else {
            setNextFormDetails(getNextForm());
            setProgress(res.progress);
            router.push(`/form?apply=APPLICATION&step=0`);
          }
        } else {
          setNextFormDetails(getNextForm());
          router.push(`/application-filter`);
        }
      }
    }
  };
  if (showWarning) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <div
          className="w-full max-w-lg p-4 bg-background border border-gray-200 shadow sm:p-6 md:p-8 flex flex-row items-center gap-x-2"
          style={{ borderRadius: 8 }}
        >
          <CrossCircledIcon
            height={40}
            width={40}
            style={{ color: "red" }} // Set to red
          />
          <p className="text-text-primary text-lg text-center">
            Agent details are required to proceed. Please provide all necessary
            details.
          </p>
        </div>
      </main>
    );
  }
  if (
    pageQueryFetching ||
    sectionQueryFetching ||
    formQueryFetching ||
    applicationDataFetching ||
    loading
  ) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }
  if (applicationData?.statusCode === 403) {
    return (
      <FormLayout pageQuery={pageQuery}>
        <div
          className="w-full max-w-lg p-4 bg-background border border-gray-200 shadow sm:p-6 md:p-8 flex flex-row items-center gap-x-2"
          style={{ borderRadius: 8 }}
        >
          <AlertCircle
            name="shield-alert"
            height={20}
            width={20}
            color={"var(--color-secondary)"}
          />

          <p className="text-text-primary text-lg text-center">
            {applicationData?.message}
          </p>
        </div>
      </FormLayout>
    );
  } else {
    return sectionQuery?.description ? (
      <FormLayout pageQuery={pageQuery}>
        <div className="flex flex-wrap sm:flex-col md:flex-row  justify-center md:mt-10 md:gap-x-8">
          <div className="w-full sm:w-[90%] md:w-[45%] md:px-16">
            <div className="prose dark:prose-invert max-w-none mb-4">
              <ReactMarkdown
                className="markDown text-sm"
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              >
                {sectionQuery?.description}
              </ReactMarkdown>
            </div>
          </div>
          <div
            className="w-full md:w-[45%] max-w-lg p-4 bg-background border border-gray-200 shadow sm:p-6 md:p-8"
            style={{ borderRadius: 8 }}
          >
            <div className="space-y-6">
              <h5
                className="font-bold text-primary dark:text-white text-center text-2xl"
                style={getBrandSpecificFontStyle(fontSize, "page-title")}
              >
                {sectionQuery?.section?.[0]?.displayName}
              </h5>

              {sortOrder(formQuery?.fieldData, "indexOrder")?.map(
                (item: any, index: any) => {
                  return (
                    <div key={index}>
                      <DynamicFields
                        register={register}
                        selectedValue={
                          watch(item?.fieldName) ||
                          watch(`${item?.documentType}`) ||
                          ""
                        }
                        disabled={
                          item?.disabledWhen
                            ? watch(item?.disabledWhen?.fieldName)?.label ===
                              item?.disabledWhen?.value
                            : false
                        }
                        isVisibleWhen
                        fieldItem={item}
                        label={
                          item?.label || item?.displayName || item?.placeholder
                        }
                        handleValueChanged={(value: any, type?: string) => {
                          if (item?.childField && item?.setValue) {
                            if (value?.value == item?.value) {
                              setValue(item?.childField, item?.setValue);
                              clearErrors(item?.childField);
                            } else {
                              setValue(item?.childField, "");
                            }
                          }
                          clearErrors(item?.fieldName);
                          clearErrors(`${item?.documentType}`);
                          if (
                            item?.type === "pickList" &&
                            item?.fieldDisplayName
                          ) {
                            setValue(
                              item?.fieldDisplayName,
                              typeof value === "object" ? value.label : value
                            );
                          }
                          if (item?.resetChild) {
                            setValue(item?.resetChild, "");
                            clearErrors(item?.resetChild);
                          }
                          setValue(item?.fieldName, value);
                        }}
                        errorMessage={
                          errors?.[item?.fieldName]?.message ||
                          errors?.[`${item?.documentType}`]?.message
                        }
                        name={item?.fieldName}
                        trigger={trigger}
                        watch={watch}
                        clearErrors={clearErrors}
                        setError={setError}
                        setValue={setValue}
                        enableAutoDetection={enableAutoDetection}
                      />
                    </div>
                  );
                }
              )}

              <div className="form-action w-full flex justify-center ">
                {formQuery?.fieldData
                  ?.filter((item: any) => item.type == "button")
                  .map((ele: any, i: number) => {
                    return (
                      <button
                        key={i}
                        onClick={() => {
                          saveData();
                        }}
                        className={`text-background rounded  bg-secondary w-full hover:bg-primary font-bold  text-sm px-5 py-2.5 mt-5  mb-2 ${
                          !watch("privacyPolicyConsent") &&
                          pageQuery?.needPrivacyPolicyConsent
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:bg-secondary"
                        }`}
                      >
                        {saving ? (
                          <div className=" w-full flex items-center justify-center">
                            <Image
                              priority
                              src={loader2}
                              height={20}
                              width={20}
                              alt="Follow us on Twitter"
                            />
                          </div>
                        ) : (
                          ele?.placeholder
                        )}
                      </button>
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
      </FormLayout>
    ) : (
      <FormLayout pageQuery={pageQuery}>
        <div
          className=" w-full max-w-lg p-10 bg-background border border-gray-200 shadow sm:p-12 md:p-16"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-6">
            <h5 className="font-bold text-primary dark:text-white text-center text-2xl">
              {sectionQuery?.section?.[0]?.displayName}
            </h5>

            {sortOrder(formQuery?.fieldData, "indexOrder")?.map(
              (item: any, index: any) => {
                return (
                  <div key={index}>
                    <DynamicFields
                      register={register}
                      selectedValue={
                        watch(item?.fieldName) ||
                        watch(`${item?.documentType}`) ||
                        ""
                      }
                      disabled={
                        item?.disabledWhen
                          ? watch(item?.disabledWhen?.fieldName)?.label ===
                            item?.disabledWhen?.value
                          : false
                      }
                      isVisibleWhen
                      fieldItem={item}
                      label={
                        item?.label || item?.displayName || item?.placeholder
                      }
                      handleValueChanged={(value: any, type?: string) => {
                        if (item?.childField && item?.setValue) {
                          if (value?.value == item?.value) {
                            setValue(item?.childField, item?.setValue);
                            clearErrors(item?.childField);
                          } else {
                            setValue(item?.childField, "");
                          }
                        }
                        clearErrors(item?.fieldName);
                        clearErrors(`${item?.documentType}`);
                        if (
                          item?.type === "pickList" &&
                          item?.fieldDisplayName
                        ) {
                          setValue(
                            item?.fieldDisplayName,
                            typeof value === "object" ? value.label : value
                          );
                        }
                        if (item?.resetChild) {
                          setValue(item?.resetChild, "");
                          clearErrors(item?.resetChild);
                        }
                        setValue(item?.fieldName, value);
                      }}
                      errorMessage={
                        errors?.[item?.fieldName]?.message ||
                        errors?.[`${item?.documentType}`]?.message
                      }
                      name={item?.fieldName}
                      trigger={trigger}
                      watch={watch}
                      clearErrors={clearErrors}
                      setError={setError}
                      setValue={setValue}
                    />
                  </div>
                );
              }
            )}
            {pageQuery?.needPrivacyPolicyConsent && (
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <Input
                      id="privacyPolicyConsent"
                      type="checkbox"
                      {...register("privacyPolicyConsent", {
                        required:
                          "You must accept the terms and conditions to proceed",
                      })}
                      className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary"
                    />
                  </div>
                  <div className="ml-3 text-xs text-gray-600 w-[90%]">
                    <Label
                      htmlFor="privacyPolicyConsent"
                      className="text-text-primary text-xs inline"
                    >
                      <ReactMarkdown
                        className="markDown inline"
                        components={{
                          a: LinkRenderer,
                          p: ({ node, ...props }) => <span {...props} />,
                        }}
                      >
                        {formQuery?.termsAndConditionData?.termText}
                      </ReactMarkdown>
                      <span className="text-error">*</span>
                    </Label>
                  </div>
                </div>
                {errors.privacyPolicyConsent && (
                  <p className="text-error text-sm mt-1">
                    {errors.privacyPolicyConsent.message as string}
                  </p>
                )}
              </div>
            )}
            <div className="form-action w-full flex justify-center ">
              {formQuery?.fieldData
                ?.filter((item: any) => item.type == "button")
                .map((ele: any, i: number) => {
                  return (
                    <button
                      key={i}
                      onClick={() => {
                        saveData();
                      }}
                      className={`text-background rounded  bg-secondary w-full hover:bg-primary font-bold  text-sm px-5 py-2.5 mt-5  mb-2 ${
                        !watch("privacyPolicyConsent") &&
                        pageQuery?.needPrivacyPolicyConsent
                          ? "opacity-50 cursor-not-allowed"
                          : "hover:bg-secondary"
                      }`}
                    >
                      {saving ? (
                        <div className=" w-full flex items-center justify-center">
                          <Image
                            priority
                            src={loader2}
                            height={20}
                            width={20}
                            alt="Follow us on Twitter"
                          />
                        </div>
                      ) : (
                        ele?.placeholder
                      )}
                    </button>
                  );
                })}
            </div>
          </div>
        </div>
      </FormLayout>
    );
  }
}
