"use client";
import FormContainer from "@/components/custom/form-container";
import { Label } from "@/components/ui/label";
import { useFormContext } from "react-hook-form";
import { useEffect, useMemo, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "../ui/skeleton";
import { useProgress } from "@/lib/progress-context";
import TimelineSession from "../custom/timeline-session";
import { useRouter, useSearchParams } from "next/navigation";
import { useFile } from "@/hooks/useFile";
import {
  getAllSectionForms,
  getOapForm,
  getOapFormSections,
  getStudentDetails,
  saveOapForm,
  getOapDetail,
} from "@/api/api";
import Header from "../custom/header";
import { useAtom } from "jotai";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  preferredLanguage,
  programmeName,
  routes,
  staticContentsAtom,
  fontSize<PERSON>tom,
} from "@/lib/atom";
import { toast } from "react-hot-toast";
import { AlertCircle, ChevronDown } from "lucide-react";
import MobileTimelineSession from "../custom/mobile-timeline-session";
import Image from "next/image";
import loader2 from "../../public/loader2.svg";
import { isValidPhoneNumber } from "libphonenumber-js";
import LanguageSelector from "../custom/translate";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

function SectionalForm() {
  const router = useRouter();

  const queryClient = useQueryClient();
  const [staticContents] = useAtom(staticContentsAtom);

  const [validationError, setValidationError] = useState<any>();
  const [saving, setSaving] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { setProgress, progress } = useProgress();
  const [subSectionArray, setSubSectionArray] = useState<number[]>([]);
  const [subSectionCount, setSubSectionCount] = useState<number>(0);
  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [userEmail] = useAtom(email);
  const [application] = useAtom(applicationId);
  const [route] = useAtom(routes);
  const [apiKey] = useAtom(consumerAPIKey);
  const [, setProgrammeName] = useAtom(programmeName);
  const [triggerError, setTriggerError] = useState(false);
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);

  const [fontSize] = useAtom(fontSizeAtom);

  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
    resetField,
  } = useFormContext();

  const { uploadFile, deleteFile } = useFile();
  const [staticContent] = useAtom<any>(staticContentsAtom);

  const reverseTransformDropdowns = (
    formData: Record<string, string | string[] | undefined>
  ) => {
    const reversedData: Record<string, string | string[] | undefined> = {};

    for (const [key, value] of Object.entries(formData)) {
      reversedData[key] = value;
    }

    return reversedData;
  };

  const searchParams = useSearchParams();
  const apply: any = searchParams.get("apply");
  const step: any = searchParams.get("step");

  const { data: pageQuery } = useQuery({
    queryKey: [`${nextFormDetails?.oap}-${nextFormDetails?.mode}`],
    queryFn: async () => {
      return await getOapDetail(
        {
          name: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
    },
    enabled: !!nextFormDetails?.oap && !!nextFormDetails?.mode,
  });

  const enableAutoDetection = useMemo(
    () => pageQuery?.enableAutoDetection,
    [pageQuery?.enableAutoDetection]
  );

  const {
    data: studentDetails,
    refetch: refetchStudent,
    isFetching: studentDetailsIsFetching,
  } = useQuery({
    queryKey: [`${userEmail}-form`],
    queryFn: async () => {
      if (userEmail) {
        let res: any = await getStudentDetails(
          nextFormDetails?.oap,
          userEmail,
          application,
          apiKey
        );
        setProgrammeName(res?.programDisplayName);

        return res;
      } else {
        return;
      }
    },
    enabled: !!(nextFormDetails && userEmail && application),
  });

  useEffect(() => {
    let temp: any = queryClient.getQueryData([`${userEmail}-form`]);
    if (temp) {
      setProgrammeName(temp?.programDisplayName);
      Object.entries(reverseTransformDropdowns(temp)).forEach(([k, val]) => {
        setValue(k, val);
      });
    }
  }, [userEmail, studentDetails]);

  const {
    data: sectionQuery,
    isFetching: sectionQueryIsFetching,
    refetch: refetchSections,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.[apply]}-form`,
    ],
    queryFn: async () => {
      if (nextFormDetails?.type == "single") {
        if (
          nextFormDetails?.form &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.form,
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey
          );
          getFiltered(res?.section, studentDetails);
          return res;
        }
      } else {
        if (
          nextFormDetails?.[apply] &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.[apply],
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey
          );
          getFiltered(res?.section, studentDetails);
          return res;
        } else {
          return;
        }
      }
    },
    enabled: true && !!studentDetails,
  });

  const { data: allSections, refetch } = useQuery({
    queryKey: [`all-section-forms`],
    queryFn: async () => {
      const res = await getAllSectionForms(
        {
          oapName: nextFormDetails?.oap,
          formName:
            nextFormDetails?.type === "single"
              ? nextFormDetails?.form
              : nextFormDetails?.[apply],
          mode: nextFormDetails?.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      if (res?.length > 0) {
        let temp = getFiltered(res, studentDetails);
        localStorage.setItem("sections", JSON.stringify(temp));
        return temp;
      }
      return [];
    },
    enabled: !!(nextFormDetails?.type === "single"
      ? nextFormDetails?.form && studentDetails
      : nextFormDetails?.[apply] &&
        nextFormDetails?.oap &&
        nextFormDetails?.mode &&
        studentDetails),
  });
  const getFiltered = (sectionData: any, student: any) => {
    let temp = sectionData.filter((item: any, i: number) => {
      if (item?.visibleWhen) {
        let obj = item.visibleWhen;
        const watchedValue =
          watch(obj?.fieldName)?.value || student?.[obj?.fieldName];

        if (obj?.condition === "notEqual") {
          const valuesToCheck =
            obj?.value?.flatMap((v: any) =>
              Array.isArray(v?.value) ? v.value : [v?.value]
            ) || [];

          // Check if watchedValue exists in the extracted list
          const result = !valuesToCheck.includes(watchedValue);
          if (!result) {
            resetField("eqheDetails");
          }
          return result ? item : false;
        }

        return student?.[obj?.fieldName] == obj?.value ? item : false;
      } else return item;
    });
    temp.sort((a: any, b: any) => a.displayOrder - b.displayOrder);
    return temp;
  };
  const responses: any = useMemo(() => {
    if (allSections) {
      return getFiltered(allSections, studentDetails);
    }
    return [];
  }, [allSections]);

  const {
    data: formQuery,
    isFetching: formQueryIsFetching,
    refetch: refetchForm,
  } = useQuery({
    // next sections page
    queryKey: [
      `${nextFormDetails?.oap}-${apply}-${step}-${sectionQuery?.SK}-${sectionQuery?.section?.[step]?.name}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[step]?.section && sectionQuery?.form))
        return;
      let temp = getFiltered(sectionQuery?.section, studentDetails);
      sectionQuery.section = temp;
      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: sectionQuery?.form,
          sectionName: sectionQuery?.section?.[step]?.section,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );

      // checking the number of sub section
      let subSectionCount: number[] = [];

      res?.fieldData?.forEach((item: any) => {
        if (item?.sectionCanRepeat) {
          const length = studentDetails?.[item?.fieldName]?.length || 0;
          let obj = item?.minLengthWhen;
          let requiredLength =
            studentDetails[obj?.fieldName] == obj?.value &&
            studentDetails[obj?.fieldName] !== undefined
              ? obj?.minLength
              : item?.minLength;

          const iterations = length === 0 ? requiredLength : length;

          for (let i = 0; i < iterations; i++) {
            subSectionCount.push(i);
          }
        }
      });

      setSubSectionArray(subSectionCount);
      setSubSectionCount(subSectionCount?.length);
      let tempObj = { ...studentDetails, ...getValues() };
      Object.entries(reverseTransformDropdowns(tempObj)).forEach(([k, val]) => {
        setValue(k, val);
      });
      return res;
    },
    enabled: true && !!sectionQuery && !!studentDetails,
  });

  useEffect(() => {
    const getValidate = async () => {
      if (
        validationError?.some(
          (item: any) => item?.missingFieldsExist === true
        ) &&
        !formQueryIsFetching &&
        !sectionQueryIsFetching &&
        triggerError
      ) {
        await trigger();
      }
    };

    getValidate();
  }, [
    validationError,
    trigger,
    step,
    formQueryIsFetching,
    sectionQueryIsFetching,
  ]);

  useEffect(() => {
    if (apiKey && Object.keys(nextFormDetails).length > 0 && studentDetails) {
      refetch();
      refetchForm();
      refetchSections();
      refetchStudent();
    }
  }, [preferLang]);

  const getSideBarStatus = (sectionName: any) => {
    const resultArray: any = [];
    if (!studentDetails) {
      return;
    }
    responses?.forEach((section: any) => {
      const sectionResult = {
        displayName: section.displayName,
        missingFieldsCount: 0,
        missingFieldsExist: false,
      };
      if (section?.displayName !== "Review & Submit" || section?.validate) {
        section?.fieldData?.forEach((field: any) => {
          const fieldName = field?.fieldName;
          const isRequired = field?.required;
          const subSection = field?.subSection;
          const subSectionRepeat = field?.sectionCanRepeat;

          if (field.requiredWhen) {
            const isNotValid = validateRequiredWhen(
              studentDetails,
              field.requiredWhen
            );

            if (isNotValid !== undefined && !isNotValid) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            } else {
              studentDetails[field?.requiredWhenFieldName] = true;
            }
          }

          if (subSection && subSectionRepeat) {
            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              const required = section?.required;

              if (studentDetails?.[subSection]) {
                studentDetails?.[subSection]?.map((item: any) => {
                  const visibleWhen = section?.visibleWhen
                    ? Array.isArray(section.visibleWhen)
                      ? section.visibleWhen.some(
                          (condition: any) =>
                            item[condition?.fieldName] === condition?.value
                        )
                      : item[section?.visibleWhen?.fieldName] ===
                        section?.visibleWhen?.value
                    : true;
                  if (required && !(fieldData in item) && visibleWhen) {
                    sectionResult.missingFieldsCount++;
                    sectionResult.missingFieldsExist = true;
                  }
                });
              }
            });
          }
          if (subSection && !subSectionRepeat) {
            const visibleWhen = field?.visibleWhen
              ? studentDetails[field?.visibleWhen?.fieldName] ===
                field?.visibleWhen?.value
              : true;

            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              let required = section?.required;
              const mandatoryWhen = section?.mandatoryWhen;
              if (mandatoryWhen) {
                if (
                  mandatoryWhen?.condition == "greaterThan" &&
                  mandatoryWhen?.fieldValue <=
                    studentDetails[mandatoryWhen?.fieldName]
                ) {
                  required = true;
                } else if (mandatoryWhen?.condition == "notEqualTo") {
                  if (
                    mandatoryWhen?.fieldValue !==
                    studentDetails?.[mandatoryWhen?.fieldName]
                  ) {
                    required = true;
                  } else if (
                    mandatoryWhen?.fieldValue ===
                    studentDetails?.[mandatoryWhen?.fieldName]
                  ) {
                    required = false;
                  }
                }
              }
              let visibleWhenUnderSection: boolean;
              if (Array.isArray(section?.visibleWhen?.value)) {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? section?.visibleWhen?.value?.includes(
                        studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                }
              } else {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? studentDetails[section?.visibleWhen?.fieldName] ===
                      section?.visibleWhen?.value
                    : true;
                }
              }

              if (
                required &&
                !(fieldData in studentDetails) &&
                visibleWhenUnderSection &&
                visibleWhen
              ) {
                sectionResult.missingFieldsCount++;
                sectionResult.missingFieldsExist = true;
              }
            });
          } else {
            if (
              isRequired &&
              fieldName &&
              !field?.visibleWhen &&
              (studentDetails?.[fieldName] === "" ||
                studentDetails?.[fieldName] === false)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }

            const visibleWhen = checkVisibility(field.visibleWhen);

            if (isRequired && !(fieldName in studentDetails) && visibleWhen) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }
          }
        });
        resultArray.push(sectionResult);
      }
    });
    const currentSectionName = resultArray?.find(
      (item: any) => item?.displayName === sectionName
    );
    if (currentSectionName?.missingFieldsExist === false) {
      return "completed";
    } else return "";
  };

  useEffect(() => {
    const saveFormData = async () => {
      if (apiKey && Object.keys(nextFormDetails).length > 0 && studentDetails) {
        refetch();
        refetchSections();
      }
    };
    if (sectionQuery?.dependencies?.refetchAllSections) {
      saveFormData();
    }
  }, [watch(sectionQuery?.dependencies?.refetchAllSections)]);
  interface DropdownItem {
    label: string;
    //@ts-ignore
    value: any;
  }
  const transformDropdowns = (
    formData: Record<string, unknown>
  ): Record<string, any> => {
    const transformedData: Record<string, any> = {};

    for (const [key, value] of Object.entries(formData)) {
      if (Array.isArray(value) && typeof value[0] == "object") {
        let newValue = [...value];

        newValue?.map((item, i) => {
          for (const [key1, value1] of Object.entries(item)) {
            if (value1 && typeof value1 === "object") {
              if (!("label" in value1) && !("value" in value1)) {
                newValue[i][key1] = value1;
              } else {
                const labelKey = `${key1}DisplayName`;
                newValue[i][labelKey] = (value1 as DropdownItem).label;
                newValue[i][key1] = (value1 as DropdownItem).value;
              }
            } else if (value1 === "" || value1 === undefined) {
              delete newValue[i][key1];
            } else if (typeof value1 !== "object") {
              newValue[i][key1] = value1;
            }
          }
        });
        transformedData[key] = newValue;
      } else if (value && typeof value === "object") {
        if (!("label" in value) && !("value" in value)) {
          transformedData[key] = value;
          if (Object.keys(value).length == 0) {
            delete transformedData[key];
          }
        } else {
          const labelKey = `${key}DisplayName`;
          transformedData[labelKey] = (value as DropdownItem).label;
          transformedData[key] = (value as DropdownItem).value;
        }
      } else if (value && typeof value !== "object" && value !== "") {
        transformedData[key] = value === "on" ? !!value : value;
      } else if (
        !isNaN(value as number) &&
        typeof value !== "object" &&
        value !== ""
      ) {
        transformedData[key] = value;
      }
    }
    // this part is for subsection or documents upload
    let dataTransformed = attachDocId(transformedData, formQuery?.fieldData);
    return dataTransformed;
  };

  const attachDocId = (rawData: any, dataList: any[]) => {
    dataList?.forEach((item: any) => {
      if (item?.type === "document") {
        if (rawData[item?.fieldName]?.[0]?.hasOwnProperty("documentId")) {
          rawData[item?.fieldName] = rawData[item?.fieldName];
        } else rawData[item?.fieldName] = [];
        if (rawData[item?.fieldName]?.length == 0) {
          delete rawData[item?.fieldName];
        }
      } else if (item?.type === "multiDocument") {
        if (Array.isArray(rawData?.[item?.fieldName])) {
          let temp = rawData?.[item?.fieldName]?.map((e: any) => e);
          rawData[item?.fieldName] = temp;
        }
        if (Object.keys(rawData[item?.fieldName] || {}).length === 0) {
          delete rawData[item?.fieldName];
        }
      }
      if (item?.type == "subsection" && item?.sectionCanRepeat) {
        rawData[item?.fieldName]?.forEach((e: any, i: any) => {
          attachDocId(e, item?.[item?.fieldName]?.fieldData);
        });
      }
      if (item?.type == "subsection" && !item?.sectionCanRepeat) {
        attachDocId(rawData, item?.[item?.fieldName]?.fieldData);
      }
    });
    return rawData;
  };

  function validateRequiredWhen(
    dataTransformed: any,
    requiredWhen: Array<any>
  ): any {
    if (
      !dataTransformed.priorInstitutionsAttended ||
      !Array.isArray(dataTransformed.priorInstitutionsAttended)
    ) {
      return;
    }

    for (const condition of requiredWhen) {
      const { hasValue, hasValueFieldName, notHasValueFieldName, notHasValue } =
        condition;

      if (dataTransformed[hasValueFieldName] === hasValue) {
        if (
          dataTransformed.priorInstitutionsAttended.some(
            (item: any) => item[notHasValueFieldName] === notHasValue
          )
        ) {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  function isFieldVisible(field: any, dataTransformed: any) {
    if (!field?.visibleWhen?.fieldName || !field?.visibleWhen?.value) {
      return false;
    }

    const fieldName = field.visibleWhen.fieldName;
    const expectedValue = field.visibleWhen.value;

    const influencedArray = dataTransformed[fieldName];

    return (
      Array.isArray(influencedArray) &&
      influencedArray.some((item) => item.value === expectedValue)
    );
  }

  const validateOnSubmit = (
    dataTransformed: any,
    canTriggerError?: boolean
  ) => {
    const resultArray: any = [];

    responses?.forEach((section: any) => {
      const sectionResult = {
        displayName: section.displayName,
        missingFieldsCount: 0,
        missingFieldsExist: false,
        isValidateRegex: true,
      };
      if (section?.displayName !== "Review & Submit" || section?.validate) {
        section?.fieldData?.forEach((field: any) => {
          const fieldName = field?.fieldName;
          const isRequired = field?.required;
          const regex = field?.rules?.pattern;
          const subSection = field?.subSection;
          const subSectionRepeat = field?.sectionCanRepeat;

          if (canTriggerError && field?.requiredWhen) {
            const isNotValid = validateRequiredWhen(
              dataTransformed,
              field.requiredWhen
            );

            if (isNotValid !== undefined && !isNotValid) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
              dataTransformed[field?.requiredWhenFieldName] = false;
              setValue(field?.requiredWhenFieldName, false);
              setError(field?.requiredWhenFieldName, field.requiredWhenRules);
            } else {
              dataTransformed[field?.requiredWhenFieldName] = true;
              setValue(field?.requiredWhenFieldName, true);
              clearErrors([field?.requiredWhenFieldName]);
            }
          }

          if (subSection && subSectionRepeat) {
            let i = 0;
            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              const required = section?.required;
              const regex = section?.rules?.pattern;
              if (dataTransformed?.[subSection]) {
                dataTransformed?.[subSection]?.map((item: any) => {
                  const visibleWhen = section?.visibleWhen
                    ? Array.isArray(section.visibleWhen)
                      ? section.visibleWhen.some(
                          (condition: any) =>
                            item[condition?.fieldName] === condition?.value
                        )
                      : item[section?.visibleWhen?.fieldName] ===
                        section?.visibleWhen?.value
                    : true;
                  if (required && !(fieldData in item) && visibleWhen) {
                    sectionResult.missingFieldsCount++;
                    sectionResult.missingFieldsExist = true;
                  }
                  if (
                    (field?.type === "number" || field.type === "mobile") &&
                    item[fieldData]?.numberWithCode &&
                    !isValidPhoneNumber(item[fieldData]?.numberWithCode)
                  ) {
                    setValue(fieldData, "");
                    delete item[fieldData];
                    if (!triggerError) {
                      clearErrors();
                    }
                  }
                  if (
                    regex &&
                    item[fieldData] &&
                    !new RegExp(regex?.value).test(item[fieldData])
                  ) {
                    sectionResult.missingFieldsCount++;
                    sectionResult.missingFieldsExist = true;
                    sectionResult.isValidateRegex = false;
                    setValue(fieldData, "");
                    delete item[fieldData];
                    if (!triggerError) {
                      clearErrors();
                    }
                  }
                });
              }
            });
          }
          if (subSection && !subSectionRepeat) {
            const visibleWhen = field?.visibleWhen
              ? dataTransformed[field?.visibleWhen?.fieldName] ===
                field?.visibleWhen?.value
              : true;

            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              let required = section?.required;
              const mandatoryWhen = section?.mandatoryWhen;
              const regex = section?.rules?.pattern;
              if (mandatoryWhen) {
                if (
                  mandatoryWhen?.condition == "greaterThan" &&
                  mandatoryWhen?.fieldValue <=
                    dataTransformed[mandatoryWhen?.fieldName]
                ) {
                  required = true;
                } else if (mandatoryWhen?.condition == "notEqualTo") {
                  if (
                    mandatoryWhen?.fieldValue !==
                    dataTransformed?.[mandatoryWhen?.fieldName]
                  ) {
                    required = true;
                  } else if (
                    mandatoryWhen?.fieldValue ===
                    dataTransformed?.[mandatoryWhen?.fieldName]
                  ) {
                    required = false;
                  }
                }
              }
              let visibleWhenUnderSection: boolean;
              if (Array.isArray(section?.visibleWhen?.value)) {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          dataTransformed[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? section?.visibleWhen?.value?.includes(
                        dataTransformed[section?.visibleWhen?.fieldName]
                      )
                    : true;
                }
              } else {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          dataTransformed[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? dataTransformed[section?.visibleWhen?.fieldName] ===
                      section?.visibleWhen?.value
                    : true;
                }
              }
              if (
                required &&
                !(fieldData in dataTransformed) &&
                visibleWhenUnderSection &&
                visibleWhen
              ) {
                sectionResult.missingFieldsCount++;
                sectionResult.missingFieldsExist = true;
              }
              if (
                (section?.type === "number" || section.type === "mobile") &&
                dataTransformed[fieldData]?.numberWithCode &&
                !isValidPhoneNumber(dataTransformed[fieldData]?.numberWithCode)
              ) {
                setValue(fieldData, "");
                delete dataTransformed[fieldData];
                if (!triggerError) {
                  clearErrors();
                }
              }
              if (
                regex &&
                dataTransformed[fieldData] &&
                !new RegExp(regex?.value).test(dataTransformed[fieldData])
              ) {
                sectionResult.missingFieldsCount++;
                sectionResult.missingFieldsExist = true;
                sectionResult.isValidateRegex = false;
                setValue(fieldData, null);
                delete dataTransformed[fieldData];
                if (!triggerError) {
                  clearErrors();
                }
              }
            });
          } else {
            if (
              isRequired &&
              fieldName &&
              !field?.visibleWhen &&
              (dataTransformed[fieldName] === "" ||
                dataTransformed[fieldName] === false)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }

            const visibleWhen = checkVisibility(field.visibleWhen);

            if (isRequired && !(fieldName in dataTransformed) && visibleWhen) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }
            // Regex validation
            if (
              regex &&
              dataTransformed[fieldName] &&
              !new RegExp(regex?.value).test(dataTransformed[fieldName])
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
              sectionResult.isValidateRegex = false;
              setValue(fieldName, null);
              delete dataTransformed[fieldName];
              if (!triggerError) {
                clearErrors();
              }
            }
          }
        });
        resultArray.push(sectionResult);
      }
    });
    return resultArray;
  };

  const handleFormSubmit = async (isSubmit: any) => {
    let selectedData = getValues();

    const selectedDataValues = JSON.parse(JSON.stringify(selectedData));

    let dataTransformed = transformDropdowns(selectedDataValues);

    if (isSubmit) {
      setTriggerError(true);
      const result = validateOnSubmit(dataTransformed, true);
      setValidationError(result);
      if (result?.every((item: any) => item.missingFieldsExist === false)) {
        dataTransformed["applicationStatus"] = "submitted";
        const res = await saveOapForm(
          {
            ...dataTransformed,
            sectionLabel: formQuery?.label,
            localization: preferLang,
          },
          { oapName: nextFormDetails.oap, mode: nextFormDetails.mode },
          apiKey
        );

        if (res?.statusCode == 500) {
          toast.custom(
            <div className="bg-error flex items-center gap-x-1 p-2 rounded">
              <div className="w-6">
                <AlertCircle
                  name="shield-alert"
                  height={20}
                  width={20}
                  color={"var(--color-background)"}
                />
              </div>
              <p className="text-background text-sm">
                {staticContent?.errors?.application?.saveFailed ||
                  "There is an error occurred on saving application. Please contact administrator."}
              </p>
            </div>
          );
          setSaving(false);
          return;
        }
        if (res) {
          setNextFormDetails((prev: any) => ({
            ...prev,
            currentOap: apply,
          }));
        }
        setProgress(res?.progress);
        // Get the current query parameters
        const searchParams: any = new URLSearchParams(window.location.search);
        searchParams.set("step", Number(step) + 1);
        const updatedQueryString = searchParams.toString();
        if (route?.length - 1 != step) {
          router.push(`/form?${updatedQueryString}`);
        } else {
          router.replace("/thank-you");
        }
      } else {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">
              {staticContent?.errors?.application?.requiredFields ||
                "You still have to fill all required fields"}
            </p>
          </div>
        );
        setSaving(false);
        return;
      }
    } else {
      if (isSubmit !== null && sectionQuery.canRefreshSection) {
        refetchSections();
      }
      const result = validateOnSubmit(dataTransformed, triggerError);
      setValidationError(result);
      const res = await saveOapForm(
        {
          email: userEmail,
          applicationId: application,
          applicationStatus: "inProgress",
          ...dataTransformed,
          sectionLabel: formQuery?.label,
          localization: preferLang,
        },
        { oapName: nextFormDetails.oap, mode: nextFormDetails.mode },
        apiKey
      );
      if (res) {
        setNextFormDetails((prev: any) => ({
          ...prev,
          currentOap: apply,
        }));
        refetchStudent();
      }
      setProgress(res?.progress);
      // Get the current query parameters
      const searchParams: any = new URLSearchParams(window.location.search);
      searchParams.set("step", Number(step) + 1);
      const updatedQueryString = searchParams.toString();
      if (isSubmit == false) {
        router.push(`/form?${updatedQueryString}`);
      }
    }
    if (!isSubmit) {
      setSaving(false);
    }
  };

  const getButtonPlaceholder = () => {
    const placeholder = formQuery?.fieldData?.find(
      (item: any) => item?.type === "button"
    )?.placeholder;
    return placeholder;
  };

  const handleFileUpload = async (payload: any, fieldName: any) => {
    try {
      const response: any = await uploadFile({
        email: userEmail,
        applicationId: application,
        oapName: nextFormDetails?.oap,
        businessUnitFilter: studentDetails?.businessUnitFilter,
        payload,
      });
      if ("successOcrFields" in response) {
        const ocrReprocessCount = watch("ocrReprocessCount");
        setValue("ocrReprocessCount", ocrReprocessCount + 1);
      } // setSaving((prev) => !prev);
      // handleFormSubmit(null);
      if (
        response?.progressPercentage ||
        response?.response ||
        response?.status
      ) {
        if (payload?.processOcr === true) {
          if (
            Object.keys(response.ocrFields).length > 0 &&
            response.successOcrFields
          ) {
            setValue("isSuccessfullExtraction", true);
            setValue("ocrExtractedResponse", response.ocrFields);
            for (const key in response.ocrFields) {
              setValue(key, response.ocrFields[key]);
              clearErrors(key);
            }
          } else {
            setValue("isSuccessfullExtraction", false);
          }
        }
        // setProgress(response?.progressPercentage);
        // setEmail(email);
        setValue(fieldName, response);
        return {
          success: true,
          document: response.response,
          ocrFields: response?.ocrFields,
          successOcrFields:
            Object.keys(response.ocrFields).length > 0 &&
            response.successOcrFields,
        };
      } else {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">{response?.message}</p>
          </div>
        );
        return { success: false };
      }
    } catch (error) {
      setSaving((prev) => !prev);
      console.log({ error });
    }
  };

  const handleDisableButton = () => {
    const isSuccessfullExtraction = watch("isSuccessfullExtraction");
    const isIdentitySelected = watch("isIdentityInformationAccurate");
    const ocrReprocessCount = watch("ocrReprocessCount");
    const maxOcrReprocess = formQuery?.maxOcrReprocess;

    if (formQuery?.isOcrReprocess) {
      if (isSuccessfullExtraction) {
        return !isIdentitySelected;
      } else {
        return ocrReprocessCount < maxOcrReprocess && !isIdentitySelected;
      }
    }

    return true;
  };
  const handleDeleteFile = async (payload: any) => {
    setSaving((prev) => !prev);
    await deleteFile({
      payload: payload?.fileData,
      studentDetail: {
        email: userEmail,
        oapName: nextFormDetails?.oap,
        applicationId: application,
        name: payload?.fileData?.name,
        type: payload?.documentType,
        documentId: payload?.fileData?.documentId,
      },
    });
    handleFormSubmit(null);
  };

  const getLabel = (sections: any) => {
    const sectionLength = sections?.length;
    const section = sections?.find(
      (_: any, index: number) => index === Number(step)
    );
    const label = `${Number(step) + 1}/${sectionLength} ${
      section?.displayName
    }`;

    return label;
  };
  const handleAddMore = () => {
    setSubSectionArray((prevIndexes: any): any => [
      ...prevIndexes,
      subSectionCount,
    ]);
    setSubSectionCount((prevCount: any): any => prevCount + 1);
  };

  const handleRemove = async (index: any, fieldItem: any) => {
    const fieldName: any = fieldItem?.subSection;
    setSubSectionArray((prevIndex: any): any => [
      ...prevIndex.filter((item: any) => item !== index),
    ]);
    setSubSectionCount((prevCounter: any): any => prevCounter - 1);

    fieldItem?.[fieldName]?.fieldData?.forEach((ele: any, i: number) => {
      if (ele.type == "document" || ele.type == "multiDocument") {
        const ids = watch(fieldName)?.[index]?.[ele?.fieldName];
        if (Array.isArray(ids))
          ids?.forEach((item: any, i: number) => {
            handleDeleteFile({ fileData: item, documentType: item?.type });
          });
      }
    });

    const fieldRemoved = watch(fieldName)?.filter(
      (item: any, i: any) => index !== i
    );

    setValue(fieldName, fieldRemoved);
    await handleFormSubmit(null);
  };

  const checkVisibility = (enableProps: any) => {
    if (!enableProps) {
      return true;
    }

    if (!enableProps) {
      return true;
    }
    const fieldWatchValue = watch(enableProps.fieldName);
    let isArr = Array.isArray(fieldWatchValue);
    const { condition, value } = enableProps;

    if (Array.isArray(enableProps?.value) && condition !== "notEqual") {
      return enableProps?.value.some((item: string) => {
        if (typeof fieldWatchValue === "string") {
          return item === (fieldWatchValue as string);
        }

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null) {
          return (
            item === fieldWatchValue?.value || item === fieldWatchValue?.label
          );
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => item === v?.value || item === v?.label || item === v
          );
        }

        return false;
      });
    }

    if (
      (enableProps.condition === "notAnd" || enableProps.condition === "and") &&
      Array.isArray(enableProps.rules)
    ) {
      const allRulesTrue = enableProps.rules.every((rule: any) => {
        const value = watch(rule.fieldName);
        const isArr = Array.isArray(value);

        if (typeof value === "object" && !isArr) {
          return value?.value === rule.value || value?.label === rule.value;
        }

        if (isArr) {
          return value.some(
            (v: any) =>
              v?.value === rule.value ||
              v?.label === rule.value ||
              v === rule.value
          );
        }

        return value === rule.value;
      });

      return enableProps.condition === "notAnd" ? !allRulesTrue : allRulesTrue;
    }

    if (condition === "exists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !!fieldWatchValue;
    }

    if (condition === "notExists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !fieldWatchValue;
    }

    if (typeof fieldWatchValue == "object" && !isArr) {
      if (condition === "notEqual") {
        return !value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (condition === "equal") {
        return (
          value.some(
            (item: { value: string }) => item.value === fieldWatchValue?.value
          ) ||
          value.some(
            (item: { label: string }) => item.label === fieldWatchValue?.label
          )
        );
      }
      if (fieldWatchValue?.value == enableProps?.value) return true;
      return false;
    }
    if (enableProps?.value) {
      if (condition === "notEqual") {
        return !(
          enableProps?.value.some(
            (item: { value: string }) => item.value === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            )) ||
          (Array.isArray(enableProps?.value) &&
            enableProps?.value.includes(fieldWatchValue))
        );
      }
      if (condition === "equal") {
        return (
          enableProps?.value.some(
            (item: { value: string }) => item.value === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            )) ||
          (Array.isArray(enableProps?.value) &&
            enableProps?.value.includes(fieldWatchValue))
        );
      }
      return fieldWatchValue === enableProps?.value ||
        (isArr &&
          fieldWatchValue?.some((obj: any) => obj.value === enableProps?.value))
        ? true
        : false;
    }
    return true;
  };

  return (
    <>
      <div className="hidden lg:flex md:flex min-w-[316px] p-4 pl-5 h-screen bg-on-background overflow-scroll flex-col justify-between">
        <div>
          <TimelineSession
            validationError={validationError}
            progressValue={progress || studentDetails?.progress}
            getSideBarStatus={getSideBarStatus}
            handleFormSubmit={handleFormSubmit}
            allSections={allSections}
            appId={studentDetails?.appId}
            canDisableTabs={
              formQueryIsFetching || sectionQueryIsFetching || saving
            }
            triggerError={triggerError}
            completedText={sectionQuery?.progressInfo?.displayName}
          />
        </div>
        {sectionQuery?.languageData &&
          sectionQuery?.languageData?.canShowLanguageSwitch && (
            <div className="w-1/2 mt-8">
              <LanguageSelector languageData={sectionQuery?.languageData} />
            </div>
          )}
      </div>

      <div className="flex min-h-screen flex-col overflow-scroll w-full">
        <div className="sticky top-0 h-20 z-50 pt-0">
          <Header
            logo={sectionQuery?.displayNameLogo}
            sectionQueryIsFetching={sectionQueryIsFetching}
            image={sectionQuery?.logoInfo?.signedUrl}
            logoInfo={sectionQuery?.logoInfo}
            title={
              studentDetails?.[sectionQuery?.displayName] ||
              studentDetails?.[sectionQuery?.displayNameField]
            }
          />
        </div>

        <div
          className={`lg:hidden md:hidden pt-6 pb-3 bg-primary items-center pl-5 `}
          onClick={() => {
            setIsDialogOpen(true);
          }}
        >
          {formQueryIsFetching || sectionQueryIsFetching ? (
            <div className="flex w-full">
              <Image
                priority
                src={loader2}
                height={32}
                width={32}
                style={{ objectFit: "contain" }}
                alt="loader"
              />
            </div>
          ) : (
            <div className="flex w-full items-center justify-between py-1">
              <Label className="text-background text-center pl-4">
                {getLabel(allSections)}
              </Label>
              <ChevronDown className="h-6 w-6 mr-4 " color="white" />
            </div>
          )}
        </div>

        {isDialogOpen ? (
          <div className=" fixed  left-0 h-screen w-full bg-gray-600 flex flex-col justify-center items-center bg-opacity-50 z-50">
            <div
              className=" bg-primary py-10 px-10 relative min-[378px]:w-[80%]"
              style={{ overflowY: "scroll" }}
            >
              <div
                className="absolute top-4 right-4 bg-white h-4 w-4 flex items-center justify-center cursor-pointer"
                onClickCapture={() => {
                  setIsDialogOpen(false);
                }}
              >
                <Label className="text-onNeutral text-center text-sm cursor-pointer">
                  x
                </Label>
              </div>
              <MobileTimelineSession
                validationError={validationError}
                items={allSections}
                setIsDialogOpen={setIsDialogOpen}
                progressValue={progress || studentDetails?.progress}
                getSideBarStatus={getSideBarStatus}
                handleFormSubmit={handleFormSubmit}
                appId={studentDetails?.appId}
              />
            </div>
          </div>
        ) : null}

        {formQueryIsFetching || sectionQueryIsFetching ? (
          <div className="pt-[35px] pb-[120px] pl-4 pr-4 md:pl-20 md:pr-4  mt-4 flex h-full flex-col overflow-scroll">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14].map(
              (e: any, i: number) => (
                <div key={i}>
                  <Skeleton className="w-[40%] lg:w-[20%] h-[20px] rounded-md bg-border mb-2 mt-2" />
                  <Skeleton className="lg:w-[50%] h-[40px] rounded-md bg-border mt-2" />
                </div>
              )
            )}
          </div>
        ) : (
          <div className=" sm:w-[100%] md:w-[100%] lg:w-[55%] pl-4 pr-4 md:pl-20 md:pr-4 md:pt-[40px] pb-[45px]  mt-4">
            <form>
              <Label
                className="text-3xl font-bold"
                style={getBrandSpecificFontStyle(fontSize, "page-title")}
              >
                {formQuery?.displayName}
              </Label>

              <div>
                <div className="flex justify-between"></div>
                <FormContainer
                  register={register}
                  fieldData={formQuery?.fieldData}
                  studentDetails={studentDetails}
                  trigger={trigger}
                  setError={setError}
                  clearErrors={clearErrors}
                  watch={watch}
                  errors={errors}
                  setValue={setValue}
                  uploadDocs={handleFileUpload}
                  handleDeleteFile={handleDeleteFile}
                  handleAddMore={handleAddMore}
                  handleRemove={handleRemove}
                  subSectionArray={subSectionArray}
                  popupDetails={formQuery?.popupDetails}
                  formQuery={formQuery}
                  enableAutoDetection={enableAutoDetection}
                />
              </div>

              <div
                className={` w-fit text-background rounded bg-secondary hover:bg-primary font-bold text-sm px-5 me-2 mb-4 cursor-pointer py-2.5 ${
                  formQuery &&
                  formQuery?.isOcrReprocess &&
                  checkVisibility(formQuery?.enableButtonWhen) &&
                  handleDisableButton()
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                style={{
                  ...getBrandSpecificFontStyle(fontSize, "label"),
                }}
                onClick={() => {
                  if (saving) return;

                  if (
                    formQuery &&
                    formQuery?.isOcrReprocess &&
                    checkVisibility(formQuery?.enableButtonWhen) &&
                    handleDisableButton()
                  )
                    return;

                  const buttonFieldItem = formQuery?.fieldData?.find(
                    (item: any) => item?.type === "button"
                  );
                  const updateWhen = buttonFieldItem?.updateWhen
                    ? watch(buttonFieldItem?.updateWhen?.fieldName) ===
                      buttonFieldItem?.updateWhen?.value
                    : true;
                  if (buttonFieldItem?.fieldName && updateWhen) {
                    setValue(
                      buttonFieldItem?.fieldName,
                      buttonFieldItem?.value
                    );
                  }
                  setSaving((prev) => !prev);
                  handleFormSubmit(
                    getButtonPlaceholder() === "Submit" ||
                      getButtonPlaceholder() === "Abschicken"
                      ? true
                      : false
                  );
                }}
              >
                {saving ? (
                  <div className=" w-full  flex items-center justify-center">
                    <Image
                      priority
                      src={loader2}
                      height={20}
                      width={20}
                      alt="loader image"
                    />
                  </div>
                ) : (
                  <p>{getButtonPlaceholder()}</p>
                )}
              </div>
            </form>
          </div>
        )}
      </div>
    </>
  );
}
export default SectionalForm;
