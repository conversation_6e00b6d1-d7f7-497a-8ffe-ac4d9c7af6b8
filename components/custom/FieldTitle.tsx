import { Label } from "../ui/label";
import { useAtom } from "jotai";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
import HelpTooltip from "./HelpTooltip";

interface TitleProps {
  label: string | undefined;
  isMandatory?: boolean;
  htmlFor?: string;
  isPreview?: boolean;
  helpText?: string;
}

export const FieldTitle = ({
  label,
  isMandatory,
  htmlFor,
  isPreview,
  helpText,
}: TitleProps) => {
  const [fontSize] = useAtom(fontSizeAtom);
  if (isPreview) {
    return (
      <div className="mb-1">
        <div className="flex items-center gap-2">
          <Label htmlFor={htmlFor} className="text-xl font-bold">
            {label || ""}
          </Label>
          <div className="flex-1">
            {helpText && <HelpTooltip helpText={helpText} iconSize={18} />}
          </div>
        </div>
      </div>
    );
  } else
    return (
      <div className="mb-1">
        <div className="flex items-center gap-2">
          <Label htmlFor={htmlFor} className="text-sm"
            style={getBrandSpecificFontStyle(fontSize, "label")}>
            {label || ""}
          </Label>
          {isMandatory && (
            <Label className="text-sm text-red-500">*</Label>
          )}
          <div className="flex-1">
            {helpText && <HelpTooltip helpText={helpText} iconSize={18} />}
          </div>
        </div>
      </div>
    );
};
