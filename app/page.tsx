"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useAtom } from "jotai";
import { applicationId, consumerAPIKey, email, nextForm } from "@/lib/atom";
import { getStudentDetails } from "@/api/api";

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const utmSource = searchParams?.get("utm_source") || null;
  const utmMedium = searchParams?.get("utm_medium") || null;
  const utmCampaign = searchParams?.get("utm_campaign") || null;
  const utmContent = searchParams?.get("utm_content") || null;
  const utmTerm = searchParams?.get("utm_term") || null;
  const utmNetwork = searchParams?.get("utm_network") || null;
  const utmReferrer = searchParams?.get("utm_referrer") || null;
  const isIframe = searchParams?.get("fromAppHero") === "true";

  const messageReceivedRef = useRef(false);
  const [countdown, setCountdown] = useState(5);

  // Atom state
  const [, setUserEmail] = useAtom(email);
  const [, setApplication] = useAtom(applicationId);
  const [, setApiKey] = useAtom(consumerAPIKey);
  const [, setNextFormDetails] = useAtom(nextForm);

  useEffect(() => {
    if (typeof window !== "undefined" && searchParams) {
      const utmParams: Record<string, string> = {};

      if (utmSource) utmParams.utmSource = utmSource;
      if (utmMedium) utmParams.utmMedium = utmMedium;
      if (utmCampaign) utmParams.utmCampaign = utmCampaign;
      if (utmContent) utmParams.utmContent = utmContent;
      if (utmTerm) utmParams.utmTerm = utmTerm;
      if (utmNetwork) utmParams.utmNetwork = utmNetwork;
      if (utmReferrer) utmParams.utmReferrer = utmReferrer;

      // Only store if there are any UTM parameters
      if (Object.keys(utmParams).length > 0) {
        console.log("setting utm params", utmParams);
        sessionStorage.setItem("utmParams", JSON.stringify(utmParams));
      }
    }
  }, [
    utmSource,
    utmMedium,
    utmCampaign,
    utmContent,
    utmTerm,
    utmNetwork,
    utmReferrer,
    searchParams,
  ]);

  // Function to decode JWT token and extract scope
  const decodeAccessToken = (accessToken: string): string | null => {
    try {
      const payload = JSON.parse(atob(accessToken.split(".")[1]));
      const scope: string = payload.scope || "";
      const match = scope.match(/x-api-key_(.+?)\//);
      return match ? match[1] : null;
    } catch (error) {
      console.error("Error decoding access token:", error);
      return null;
    }
  };

  // Function to get next form configuration
  const getNextForm = () => {
    return {
      mode: process.env.NEXT_PUBLIC_OAP_MODE,
      type: "single",
      form: "APPLICATION",
      oap: process.env.NEXT_PUBLIC_OAP_NAME,
    };
  };

  // Send iframe ready message to parent window
  useEffect(() => {
    if (!isIframe) return;

    const sendIframeReadyMessage = () => {
      try {
        const message = {
          type: "ready_state",
          timestamp: new Date().getTime(),
        };

        if (window.parent && window.parent !== window) {
          window.parent.postMessage(message, "https://stage.apphero.io");
          console.log("Sent ready_state message to parent window");
        }
      } catch (error) {
        console.error("Error sending iframe ready message:", error);
      }
    };

    const readyTimer = setTimeout(() => {
      console.log("Sending iframe ready message after 100 ms");
      sendIframeReadyMessage();
    }, 100);

    return () => {
      clearTimeout(readyTimer);
    };
  }, [isIframe]);

  // Handle authentication message from parent window
  useEffect(() => {
    if (!isIframe) return;

    let isProcessingAuth = false;

    const handleAuthMessage = async (event: any) => {
      console.log("Message event received:", event.data);

      // Security check: Validate the origin of the message
      const trustedOrigins = [
        "https://stage.apphero.io",
        "http://localhost:3001",
        "*",
      ];

      if (!trustedOrigins.includes(event.origin)) {
        console.error("Message received from untrusted origin:", event.origin);
        return;
      }

      if (isProcessingAuth || !event || !event.data) {
        console.log(
          "Skipping event processing: already processing or invalid event"
        );
        return;
      }

      if (event?.data?.type === "AUTH_REQUEST") {
        try {
          isProcessingAuth = true;
          const {
            email: userEmail,
            applicationId: appId,
            access_token,
          } = event.data;

          console.log("Processing AUTH_REQUEST with:", { userEmail, appId });

          // Decode access token to extract scope
          const scope = decodeAccessToken(access_token);
          if (scope) {
            setApiKey(scope);
            console.log("Set API key from token scope:", scope);
          }

          // Set user data in atoms
          setUserEmail(userEmail);
          setApplication(appId);
          setNextFormDetails(getNextForm());

          // Store in localStorage
          localStorage.setItem("email", userEmail);
          localStorage.setItem("applicationId", appId);

          // Get application details
          const studentDetails = await getStudentDetails(
            process.env.NEXT_PUBLIC_OAP_NAME || "",
            userEmail,
            appId,
            scope || ""
          );

          if (studentDetails) {
            console.log("Successfully retrieved student details");

            // Use the same navigation logic as applicationForm
            if (studentDetails?.applicationStatus === "submitted") {
              router.push(`/thank-you`);
            } else if (studentDetails?.program) {
              // Set next form details based on student data
              const nextFormConfig = {
                mode: process.env.NEXT_PUBLIC_OAP_MODE,
                type: studentDetails?.applicationType ? "multiple" : "single",
                form: "APPLICATION",
                oap: process.env.NEXT_PUBLIC_OAP_NAME,
                currentOap: studentDetails?.applicationType || "APPLICATION",
              };

              setNextFormDetails(nextFormConfig);

              // Navigate based on application type
              if (studentDetails?.applicationType) {
                router.push(
                  `/form?apply=${studentDetails?.applicationType}&step=0`
                );
              } else {
                router.push(`/form?apply=APPLICATION&step=0`);
              }
            } else {
              router.push(`/application`);
            }
          } else {
            console.error("Failed to retrieve student details");
            router.push("/application");
          }
        } catch (error) {
          console.error("Error processing AUTH_REQUEST:", error);
          router.push("/application");
        } finally {
          isProcessingAuth = false;
        }
      } else {
        router.push("/login");
      }
    };

    const messageEventHandler = (event: any) => {
      messageReceivedRef.current = true;
      if (event.data?.type === "AUTH_REQUEST") {
        console.log("AUTH_REQUEST detected, calling handleAuthMessage");
        handleAuthMessage(event);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("message", messageEventHandler);
    }

    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("message", messageEventHandler);
      }
    };
  }, [
    router,
    isIframe,
    setUserEmail,
    setApplication,
    setApiKey,
    setNextFormDetails,
  ]);

  // Countdown timer and fallback logic
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Update countdown every second
      const countdownInterval = setInterval(() => {
        setCountdown((prevCount: number) => {
          if (prevCount <= 1) {
            clearInterval(countdownInterval);
            return 0;
          }
          return prevCount - 1;
        });
      }, 1000);

      const fallbackTimer = () => {
        if (!isIframe && !messageReceivedRef.current) {
          // For non-iframe context, redirect to application
          router.push("/application");
        }
      };

      if (!isIframe) {
        // Immediate redirect for non-iframe context
        router.push("/application");
      } else if (isIframe) {
        // Set fallback timer for iframe context
        setTimeout(fallbackTimer, countdown * 1000);
      }

      return () => {
        clearInterval(countdownInterval);
      };
    }
  }, [router, isIframe, countdown]);

  // Test function to simulate iframe message
  const sendTestMessage = () => {
    const testMessage = {
      type: "AUTH_REQUEST",
      email: "<EMAIL>",
      applicationId: "acf0f19b-5cd0-4c3a-b98d-3d18b845d7f6",
      brand: "UCW",
      access_token:
        "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      timestamp: new Date().getTime(),
    };

    // Simulate postMessage event
    window.dispatchEvent(
      new MessageEvent("message", {
        data: testMessage,
        origin: "https://stage.apphero.io",
      })
    );
  };

  return (
    <main className="min-h-screen bg-primary flex flex-col items-center justify-center overflow-scroll">
      <div className="text-white text-center">
        <h1 className="text-2xl mb-4">Authentication in progress...</h1>
        <p>
          {isIframe
            ? "Waiting for authentication data from parent window..."
            : "Please wait while we authenticate your session."}
        </p>
        <div className="mt-3 mb-3">
          <div className="inline-block px-4 py-2 bg-white/10 rounded-full">
            <span className="text-white font-medium">
              Redirecting in {countdown} seconds
            </span>
          </div>
        </div>

        {/* Test Button for Development */}
        <div className="mt-4 mb-4">
          <button
            onClick={sendTestMessage}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium"
          >
            🧪 Test Auth Message
          </button>
        </div>

        <p className="mt-4 text-sm">
          If you&apos;re not redirected automatically,{" "}
          <button
            onClick={() => router.push("/login")}
            className="underline text-blue-300 hover:text-blue-100"
          >
            click here to login
          </button>
        </p>
      </div>
    </main>
  );
}
